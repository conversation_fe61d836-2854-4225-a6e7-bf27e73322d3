import cv2
from time import time
import numpy as np
from ultralytics.solutions.solutions import BaseSolution
from ultralytics.utils.plotting import Annotator, colors
from datetime import datetime
import mysql.connector
from paddleocr import PaddleOCR
import csv
import os


class SpeedEstimator(BaseSolution):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.initialize_region()  # Initialize speed region
        self.spd = {}  # Dictionary to store speed data
        self.trkd_ids = []  # List for already tracked and speed-estimated IDs
        self.trk_pt = {}  # Dictionary for previous timestamps
        self.trk_pp = {}  # Dictionary for previous positions
        self.logged_ids = set()  # Set to keep track of already logged IDs

        # Initialize the OCR system
        self.ocr = PaddleOCR(use_textline_orientation=True, lang='en')

        # MySQL database connection
        self.db_connection = self.connect_to_db()

        # CSV file setup
        self.csv_filename = f"detection_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        self.setup_csv_file()

    def connect_to_db(self):
        """Establish connection to MySQL database and create database/table if not exists."""
        try:
            # Connect to MySQL server
            connection = mysql.connector.connect(
                host="localhost",
                user="root",  # Replace with your MySQL username
                password=""   # Replace with your MySQL password
            )
            cursor = connection.cursor()

            # Create database if it doesn't exist
            cursor.execute("CREATE DATABASE IF NOT EXISTS numberplates_speed")
            print("Database 'numberplates_speed' checked/created.")

            # Connect to the newly created or existing database
            connection.database = "numberplates_speed"

            # Create table if it doesn't exist
            create_table_query = """
            CREATE TABLE IF NOT EXISTS my_data (
                id INT AUTO_INCREMENT PRIMARY KEY,
                date DATE,
                time TIME,
                track_id INT,
                class_name VARCHAR(255),
                speed FLOAT,
                numberplate TEXT
            )
            """
            cursor.execute(create_table_query)
            print("Table 'my_data' checked/created.")

            return connection
        except mysql.connector.Error as err:
            print(f"Error connecting to database: {err}")
            print("Running without database connection...")
            return None

    def setup_csv_file(self):
        """Setup CSV file with headers for storing detection results."""
        try:
            with open(self.csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['timestamp', 'date', 'time', 'track_id', 'class_name', 'speed_kmh', 'number_plate', 'confidence']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
            print(f"CSV file created: {self.csv_filename}")
        except Exception as e:
            print(f"Error creating CSV file: {e}")
            self.csv_filename = None

    def perform_ocr(self, image_array):
        """Performs OCR on the given image and returns the extracted text."""
        if image_array is None or image_array.size == 0:
            return ""

        # Skip OCR if image is too small (likely not a number plate)
        if image_array.shape[0] < 20 or image_array.shape[1] < 40:
            return ""

        try:
            if isinstance(image_array, np.ndarray):
                results = self.ocr.predict(image_array)
                if results and len(results) > 0 and results[0]:
                    return ' '.join([result[1][0] for result in results[0]])
                return ""
            else:
                return ""
        except Exception as e:
            print(f"OCR Error: {e}")
            return ""

    def save_to_database(self, date, time, track_id, class_name, speed, numberplate):
        """Save data to both MySQL database and CSV file."""
        # Print the detection result with number plate
        if numberplate.strip():
            print(f"🚗 DETECTED: Number Plate: [{numberplate}] | Speed: {speed} km/h | Vehicle: {class_name} | ID: {track_id}")
        else:
            print(f"🚗 DETECTED: Vehicle: {class_name} | Speed: {speed} km/h | ID: {track_id} | No plate detected")

        # Save to CSV file
        self.save_to_csv(date, time, track_id, class_name, speed, numberplate)

        # Save to database if available
        if self.db_connection is None:
            return

        try:
            cursor = self.db_connection.cursor()
            query = """
                INSERT INTO my_data (date, time, track_id, class_name, speed, numberplate)
                VALUES (%s, %s, %s, %s, %s, %s)
            """
            cursor.execute(query, (date, time, track_id, class_name, speed, numberplate))
            self.db_connection.commit()
            print(f"✅ Data saved to database")
        except mysql.connector.Error as err:
            print(f"❌ Error saving to database: {err}")

    def save_to_csv(self, date, time, track_id, class_name, speed, numberplate):
        """Save detection data to CSV file."""
        if self.csv_filename is None:
            return

        try:
            current_time = datetime.now()
            with open(self.csv_filename, 'a', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['timestamp', 'date', 'time', 'track_id', 'class_name', 'speed_kmh', 'number_plate', 'confidence']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writerow({
                    'timestamp': current_time.strftime('%Y-%m-%d %H:%M:%S'),
                    'date': date,
                    'time': time,
                    'track_id': track_id,
                    'class_name': class_name,
                    'speed_kmh': speed,
                    'number_plate': numberplate,
                    'confidence': 'N/A'  # Can be enhanced later
                })
            print(f"💾 Data saved to CSV: {self.csv_filename}")
        except Exception as e:
            print(f"❌ Error saving to CSV: {e}")

    def estimate_speed(self, im0):
        """Estimate speed of objects and track them."""
        self.annotator = Annotator(im0, line_width=self.line_width)  # Initialize annotator
        self.extract_tracks(im0)  # Extract tracks

        # Get current date and time
        current_time = datetime.now()

        for box, track_id, cls in zip(self.boxes, self.track_ids, self.clss):
            self.store_tracking_history(track_id, box)  # Store track history

            if track_id not in self.trk_pt:
                self.trk_pt[track_id] = 0
            if track_id not in self.trk_pp:
                self.trk_pp[track_id] = self.track_line[-1]

            speed_label = f"{int(self.spd[track_id])} km/h" if track_id in self.spd else self.names[int(cls)]

            # Get OCR text first to include in label
            x1, y1, x2, y2 = map(int, box)  # Convert box coordinates to integers
            cropped_image = np.array(im0)[y1:y2, x1:x2]
            ocr_text = self.perform_ocr(cropped_image)

            # Create label with number plate if available
            if ocr_text.strip():
                label = f"ID:{track_id} {speed_label} | Plate: {ocr_text}"
            else:
                label = f"ID:{track_id} {speed_label}"

            # Draw the bounding box and label
            self.annotator.box_label(box, label=label, color=colors(track_id, True))  # Draw bounding box

            # Speed and direction calculation
            if self.LineString([self.trk_pp[track_id], self.track_line[-1]]).intersects(self.r_s):
                direction = "known"
            else:
                direction = "unknown"

            # Calculate speed if the direction is known and the object is new
            if direction == "known" and track_id not in self.trkd_ids:
                self.trkd_ids.append(track_id)
                time_difference = time() - self.trk_pt[track_id]
                if time_difference > 0:
                    speed = np.abs(self.track_line[-1][1].item() - self.trk_pp[track_id][1].item()) / time_difference
                    self.spd[track_id] = round(speed)

            # Update the previous tracking time and position
            self.trk_pt[track_id] = time()
            self.trk_pp[track_id] = self.track_line[-1]

            # Get the class name and speed
            class_name = self.names[int(cls)]
            speed = self.spd.get(track_id)

            # Save detection data if not already logged and we have speed data
            if track_id not in self.logged_ids and speed is not None:
                self.save_to_database(
                    current_time.strftime("%Y-%m-%d"),
                    current_time.strftime("%H:%M:%S"),
                    track_id,
                    class_name,
                    speed,
                    ocr_text if ocr_text.strip() else "No plate detected"
                )
                self.logged_ids.add(track_id)

        self.display_output(im0)  # Display output with base class function
        return im0


# Use video file for detection
import os
if os.path.exists('tc.mp4'):
    cap = cv2.VideoCapture('tc.mp4')
    print("Using video file: tc.mp4")
else:
    print("Error: tc.mp4 not found!")
    print("Please download the video from the link in vid.txt and save it as tc.mp4")
    exit()

# Check if video opened successfully
if not cap.isOpened():
    print("Error: Could not open video file tc.mp4")
    exit()

print("Video loaded successfully!")

# Define region points for counting
region_points = [(0, 145), (1018, 145)]

# Initialize the object counter
speed_obj = SpeedEstimator(
    region=region_points,
    model="best.pt",  # Replace with your YOLO model file
    line_width=2
)

count = 0
print("Press 'q' to quit the application")
print("Processing video: tc.mp4")

# Get video properties
fps = cap.get(cv2.CAP_PROP_FPS)
total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
duration = total_frames / fps if fps > 0 else 0
print(f"Video info: {total_frames} frames, {fps:.2f} FPS, {duration:.2f} seconds")

while True:
    # Read a frame from the video
    ret, frame = cap.read()
    if not ret:
        print("End of video reached")
        break

    count += 1
    # Skip some frames for better performance (process every 3rd frame)
    if count % 3 != 0:
        continue

    # Resize frame for consistent processing
    frame = cv2.resize(frame, (1020, 500))

    # Process the frame with the speed estimator
    result = speed_obj.estimate_speed(frame)

    # Add video information overlay
    cv2.putText(result, f"YOLO11 Number Plate Detection & Speed Estimation",
                (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
    cv2.putText(result, f"Frame: {count}/{total_frames} | Press 'q' to quit",
                (10, result.shape[0] - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

    # Show the frame
    cv2.imshow("YOLO11 Number Plate Detection & Speed Estimation - Video", result)

    # Check for 'q' key press to quit
    # Use waitKey(1) for smooth video playback
    if cv2.waitKey(1) & 0xFF == ord("q"):
        break

# Release the video and close all windows
print("Shutting down...")
cap.release()
cv2.destroyAllWindows()
print("Video processing completed successfully")
