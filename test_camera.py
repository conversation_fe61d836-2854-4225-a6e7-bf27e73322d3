#!/usr/bin/env python3
"""
Simple camera test script to verify camera is working before running the main application.
"""

import cv2
import sys

def test_camera():
    """Test if camera is accessible and working."""
    print("Testing camera access...")
    
    # Try to open camera
    cap = cv2.VideoCapture(0)
    
    if not cap.isOpened():
        print("Error: Could not open camera")
        print("Please check if:")
        print("1. Camera is connected")
        print("2. Camera is not being used by another application")
        print("3. Camera drivers are installed")
        return False
    
    print("Camera opened successfully!")
    print("Press 'q' to quit the camera test")
    
    # Set camera properties
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    
    frame_count = 0
    
    while True:
        ret, frame = cap.read()
        
        if not ret:
            print("Error: Could not read frame from camera")
            break
        
        frame_count += 1
        
        # Add text overlay
        cv2.putText(frame, f"Camera Test - Frame: {frame_count}", 
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        cv2.putText(frame, "Press 'q' to quit", 
                   (10, frame.shape[0] - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
        
        # Display the frame
        cv2.imshow("Camera Test", frame)
        
        # Check for 'q' key press
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break
    
    # Clean up
    cap.release()
    cv2.destroyAllWindows()
    print("Camera test completed successfully!")
    return True

if __name__ == "__main__":
    success = test_camera()
    if success:
        print("\nCamera is working! You can now run the main application with:")
        print("python main.py")
    else:
        print("\nCamera test failed. Please fix camera issues before running main application.")
        sys.exit(1)
