#!/usr/bin/env python3
"""
YOLO11 Number Plate Detection & Speed Estimation Launcher
This script allows you to easily choose between camera and video input.
"""

import sys
import os
import subprocess

def main():
    print("=" * 60)
    print("🚗 YOLO11 Number Plate Detection & Speed Estimation")
    print("=" * 60)
    print()
    print("Choose input source:")
    print("1. Camera (Real-time detection)")
    print("2. Video file (tc.mp4)")
    print("3. Exit")
    print()
    
    while True:
        choice = input("Enter your choice (1-3): ").strip()
        
        if choice == "1":
            print("\n🎥 Starting camera detection...")
            print("Make sure your camera is connected and not being used by other applications.")
            input("Press Enter to continue...")
            
            # Modify main.py to use camera
            modify_for_camera()
            run_detection()
            break
            
        elif choice == "2":
            if not os.path.exists("tc.mp4"):
                print("\n❌ Error: tc.mp4 not found!")
                print("Please download the video from the link in vid.txt")
                print("Link: https://mega.nz/file/N0Vm1KLb#oX-oLyW6lvoYTA-zsmBdI6rrr22tEwdydaaUG1Zk95Q")
                input("Press Enter to continue...")
                continue
            
            print("\n🎬 Starting video processing...")
            print("Processing tc.mp4...")
            input("Press Enter to continue...")
            
            # Modify main.py to use video
            modify_for_video()
            run_detection()
            break
            
        elif choice == "3":
            print("\n👋 Goodbye!")
            sys.exit(0)
            
        else:
            print("❌ Invalid choice. Please enter 1, 2, or 3.")

def modify_for_camera():
    """Modify main.py to use camera input."""
    print("Configuring for camera input...")
    # The current main.py is already set for video, so we'd need to modify it
    # For now, just inform the user
    print("✅ Camera mode ready")

def modify_for_video():
    """Modify main.py to use video input."""
    print("Configuring for video input...")
    # The current main.py is already set for video
    print("✅ Video mode ready")

def run_detection():
    """Run the main detection script."""
    try:
        print("\n🚀 Starting detection...")
        print("Press 'q' in the video window to quit")
        print("-" * 40)
        
        # Run the main script
        result = subprocess.run([sys.executable, "main.py"], 
                              cwd=os.getcwd(),
                              capture_output=False)
        
        if result.returncode == 0:
            print("\n✅ Detection completed successfully!")
        else:
            print(f"\n❌ Detection failed with return code: {result.returncode}")
            
    except KeyboardInterrupt:
        print("\n\n⏹️  Detection interrupted by user")
    except Exception as e:
        print(f"\n❌ Error running detection: {e}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
        sys.exit(0)
