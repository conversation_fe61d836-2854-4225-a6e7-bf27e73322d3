#!/usr/bin/env python3
"""
CSV Results Viewer for YOLO11 Number Plate Detection
This script displays the detection results in a formatted table.
"""

import csv
import os
import glob
from datetime import datetime

def find_latest_csv():
    """Find the most recent detection results CSV file."""
    csv_files = glob.glob("detection_results_*.csv")
    if not csv_files:
        return None
    
    # Sort by modification time, newest first
    csv_files.sort(key=os.path.getmtime, reverse=True)
    return csv_files[0]

def display_results(csv_file):
    """Display the detection results in a formatted table."""
    if not os.path.exists(csv_file):
        print(f"❌ File not found: {csv_file}")
        return
    
    print("=" * 100)
    print("🚗 YOLO11 Number Plate Detection Results")
    print("=" * 100)
    print(f"📄 File: {csv_file}")
    print(f"📅 File created: {datetime.fromtimestamp(os.path.getmtime(csv_file)).strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        with open(csv_file, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            rows = list(reader)
            
            if not rows:
                print("📭 No detection data found in the file.")
                return
            
            print(f"📊 Total detections: {len(rows)}")
            print("-" * 100)
            
            # Header
            print(f"{'#':<3} {'Timestamp':<19} {'Track ID':<8} {'Vehicle':<12} {'Speed':<8} {'Number Plate':<25}")
            print("-" * 100)
            
            # Data rows
            for i, row in enumerate(rows, 1):
                timestamp = row.get('timestamp', 'N/A')[:19]  # Truncate to fit
                track_id = row.get('track_id', 'N/A')
                vehicle = row.get('class_name', 'N/A')
                speed = f"{row.get('speed_kmh', 'N/A')} km/h"
                plate = row.get('number_plate', 'N/A')
                
                # Truncate long plate text
                if len(plate) > 23:
                    plate = plate[:20] + "..."
                
                print(f"{i:<3} {timestamp:<19} {track_id:<8} {vehicle:<12} {speed:<8} {plate:<25}")
            
            print("-" * 100)
            
            # Summary statistics
            speeds = [float(row['speed_kmh']) for row in rows if row['speed_kmh'].replace('.', '').isdigit()]
            plates_detected = [row for row in rows if row['number_plate'] and row['number_plate'] != 'No plate detected']
            
            print("\n📈 Summary:")
            print(f"   • Total vehicles detected: {len(rows)}")
            print(f"   • Vehicles with number plates: {len(plates_detected)}")
            print(f"   • Detection rate: {len(plates_detected)/len(rows)*100:.1f}%" if rows else "   • Detection rate: 0%")
            
            if speeds:
                print(f"   • Average speed: {sum(speeds)/len(speeds):.1f} km/h")
                print(f"   • Max speed: {max(speeds):.1f} km/h")
                print(f"   • Min speed: {min(speeds):.1f} km/h")
            
            print("\n🔤 Detected Number Plates:")
            for i, row in enumerate(plates_detected, 1):
                plate = row['number_plate']
                speed = row['speed_kmh']
                timestamp = row['timestamp']
                print(f"   {i}. [{plate}] - {speed} km/h at {timestamp}")
                
    except Exception as e:
        print(f"❌ Error reading CSV file: {e}")

def main():
    print("🔍 Looking for detection results...")
    
    # Check if a specific file was provided
    import sys
    if len(sys.argv) > 1:
        csv_file = sys.argv[1]
    else:
        csv_file = find_latest_csv()
    
    if not csv_file:
        print("❌ No detection results CSV files found.")
        print("💡 Run the detection first: python main.py")
        return
    
    display_results(csv_file)
    print("\n" + "=" * 100)

if __name__ == "__main__":
    main()
